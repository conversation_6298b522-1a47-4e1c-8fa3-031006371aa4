from pyrogram import Client
import os
from dotenv import load_dotenv, set_key, find_dotenv
import logging

logging.basicConfig(level=logging.INFO)

class TelegramSessionManager():

    def __init__(self,account_name: str ):
        """Initialize Telegram Session Manager"""
        load_dotenv()
        self.api_id = os.getenv(f"{account_name}_api_id")
        self.api_hash = os.getenv(f"{account_name}_api_hash")

    
    def create_session_string(self, session_name: str = "my_account") -> str:
        """
        สร้าง session string (รันครั้งเดียว - จะถาม phone และ verification code)
        
        Args:
            session_name: ชื่อ session
            
        Returns:
            str: session string
        """
        app = Client(session_name, api_id=self.api_id, api_hash=self.api_hash)
        
        with app:
            self.session_string = app.export_session_string()
            logging.info(f"Session String: {self.session_string}")
            self.update_env()
            return self.session_string
    
    def get_client_with_session(self, session_string: str, session_name: str = "my_account") -> Client:
        """
        สร้าง Telegram client ด้วย session string (ไม่ต้องใส่ phone/code)
        
        Args:
            session_string: session string ที่ได้จาก create_session_string()
            session_name: ชื่อ session
            
        Returns:
            Client: Telegram client พร้อมใช้งาน
        """
        app_tg = Client(
            session_name,
            api_id=self.api_id,
            api_hash=self.api_hash,
            session_string=session_string
        )
        return app_tg
    
    def update_env(self) -> None:
        dotenv_file = find_dotenv()

        # อ่านไฟล์ทั้งหมด
        with open(dotenv_file, "r") as file:
            content = file.read()

        logging.info("=== ค่าเดิม ===")
        logging.info(content)

        # แทนที่หรือเพิ่มบรรทัดใหม่
        lines = content.split('\n')
        new_lines = []
        found = False

        for line in lines:
            if line.strip().startswith('acctest_session_string='):
                new_lines.append(f"acctest_session_string={self.session_string}")
                found = True
            else:
                new_lines.append(line)

        # ถ้าไม่เจอ ให้เพิ่มใหม่
        if not found:
            new_lines.append(f"acctest_session_string={self.session_string}")

        # เขียนกลับ
        new_content = '\n'.join(new_lines)
        with open(dotenv_file, "w") as file:
            file.write(new_content)

        logging.info("=== ค่าใหม่ ===")
        with open(dotenv_file, "r") as file:
            final_content = file.read()
            logging.info(final_content)
            