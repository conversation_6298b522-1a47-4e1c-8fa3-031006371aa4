A = {
  "contents": [
    {
      "parts": [
        {
          "text": "ผมต้องการถอดไฟล์เสียงการสนทนาระหว่าง 2 ฝ่ายนี้ที่แนบไป โดยรูปแบบข้อความที่ถอดออกมาต้องระบุผู้พูดเป็น "สมาชิก" และ "ลูกค้า" อย่างชัดเจน"
        },
        {
          "file_data": {
            "mime_type": "{{ $json.mimeType }}",
            "file_uri": "{{ $json.uri }}"
          }
        }
      ]
    }
  ],
    "generationConfig": {
        "temperature": 1,
        "topK": 40,
        "topP": 0.95,
        "maxOutputTokens": 8192,
        "responseMimeType": "text/plain"
    },
    "system_instruction": {
            "parts": {"text":"บทบาท:"
    }
        }
    
}