from TelegramSessionManager import TelegramSessionManager
import os
from dotenv import load_dotenv, set_key, find_dotenv
import re

TelegramSessionManager1 = TelegramSessionManager(account_name="acctest")
acctest_session_string = TelegramSessionManager1.create_session_string("acctest_session")
print(acctest_session_string)
# dotenv_file = find_dotenv()

# # อ่านไฟล์ทั้งหมด
# with open(dotenv_file, "r") as file:
#     content = file.read()

# print("=== ค่าเดิม ===")
# print(content)

# # แทนที่หรือเพิ่มบรรทัดใหม่
# lines = content.split('\n')
# new_lines = []
# found = False

# for line in lines:
#     if line.strip().startswith('acctest_session_string='):
#         new_lines.append(f"acctest_session_string={acctest_session_string}")
#         found = True
#     else:
#         new_lines.append(line)

# # ถ้าไม่เจอ ให้เพิ่มใหม่
# if not found:
#     new_lines.append(f"acctest_session_string={acctest_session_string}")

# # เขียนกลับ
# new_content = '\n'.join(new_lines)
# with open(dotenv_file, "w") as file:
#     file.write(new_content)

# print("=== ค่าใหม่ ===")
# with open(dotenv_file, "r") as file:
#     final_content = file.read()
#     print(final_content)