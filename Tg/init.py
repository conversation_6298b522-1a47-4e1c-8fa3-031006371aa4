from pyrogram import Client
from pyrogram.errors import Flood<PERSON>ait, UserPrivacyRestricted, PeerIdInvalid
import asyncio
import os
from dotenv import load_dotenv
from fastapi import FastAPI,HTTPException
from pydantic import BaseModel

load_dotenv()

app_api = FastAPI(
    title="Invite Telegram channal",  # title = ชื่อ API ที่จะแสดงใน documentation
    description="Invite customer to Telegram channal by username of customer and channal",  # description = คำอธิบาย API
    version="1.0.0"  # version = เวอร์ชันของ API
)

class InviteTelegramChannal(BaseModel):
    username: str
    channal: str
    account_name: str





# ใส่ค่าที่คุณได้รับ
api_id = os.getenv("acctest_api_id")  # เปลี่ยนเป็น api_id ของคุณ
api_hash = os.getenv("acctest_api_hash")  # เปลี่ยนเป็น api_hash ของคุณ
user_name = "MrPz101"
group_or_channal = os.getenv("acctest_channal")  # เปลี่ยนเป็น group หรือ channal ที่ต้องการ
session_string= os.getenv("acctest_session_string")
# สร้าง client

app_tg = Client(session_string= session_string, api_id=api_id, api_hash=api_hash,name="temp")

async def add_users_to_chat():
    async with app_tg:
        # ระบุ channel หรือ group (ใช้ username หรือ chat_id)
        chat_id = group_or_channal  # หรือใช้ username
        # ระบุ user ที่จะเพิ่ม (ใช้ username หรือ user_id)
        user_ids = [user_name]  # เปลี่ยนตามต้องการ
        
        for user in user_ids:
            try:
                await app_tg.add_chat_members(
                    chat_id=chat_id,
                    user_ids=user
                )
                print(f"✅ เพิ่ม {user} เข้า {chat_id} สำเร็จ!")
                # หน่วงเวลาเพื่อป้องกัน rate limit
                await asyncio.sleep(3)
            except UserPrivacyRestricted:
                print(f"❌ ไม่สามารถเพิ่ม {user} ได้: ตั้งค่าความเป็นส่วนตัว")
            except PeerIdInvalid:
                print(f"❌ ไม่พบ {user} หรือ {chat_id}")
            except FloodWait as e:
                print(f"⏳ ต้องรอ {e.value} วินาทีเนื่องจาก rate limit")
                await asyncio.sleep(e.value)
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดกับ {user}: {str(e)}")

# รัน code
app_tg.run(add_users_to_chat())

with app_tg:
    me = app_tg.get_me()
    print(f"ล็อกอินสำเร็จ: {me.first_name}")