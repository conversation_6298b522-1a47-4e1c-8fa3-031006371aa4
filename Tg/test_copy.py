from pyrogram import Client
import builtins
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging
logging.basicConfig(level=logging.INFO)
# สร้าง custom input function
import uvicorn
app_api = FastAPI(title="Telegram Auth Service")

class AccoutNameRequest(BaseModel):
    accout_name: str
class CodeSubmit(BaseModel):
    code: str
# Endpoints
@app_api.get("/")
async def root():
    return {"message": "Telegram Auth Service", "status": "running"}

@app_api.post("/get_info_accout")
async def create_session(account_name: AccoutNameRequest):
    logging.info(f"Account name: {account_name.accout_name}")
    return {"message": "Hello World"}
    


if __name__ == "__main__":
    uvicorn.run("test_copy:app_api", host="0.0.0.0", port=8000, reload=True)
# def custom_input(prompt=""):
#     print(prompt)
#     if "phone number" in prompt.lower():
#         return get_phone_number()
#     elif "confirmation code" in prompt.lower() or "verification code" in prompt.lower():
#         return get_verification_code()
#     elif "password" in prompt.lower():
#         return get_password()
#     else:
#         # ถ้าไม่ตรงเงื่อนไข ใช้ input ปกติ
#         return original_input(prompt)

# def get_phone_number():
#     # รับเบอร์จากช่องทางของคุณ
#     return "+***********"

# def get_verification_code():
#     # รับ code จากช่องทางของคุณ
#     print("Getting code from custom source...")
#     A = original_input("Enter verification code here: ")  # ใช้ original_input
#     return A

# def get_password():
#     # รับ password จากช่องทางของคุณ
#     return "your_password"

# # Backup original input
# original_input = builtins.input

# # Replace input with custom function
# builtins.input = custom_input

# # ใช้ Pyrogram ปกติ
# app = Client(
#     "my_session",
#     api_id="24308068",
#     api_hash="efbca126d47aae49208ab3c16954248e",
#     phone_number="+***********"
    
# )

# with app:
#     me = app.get_me()
#     print(f"Logged in as {me.first_name}")

# # Restore original input
# builtins.input = original_input

